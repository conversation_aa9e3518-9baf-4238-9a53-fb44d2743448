const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get capacity data for all labs
router.get('/', authenticateToken, async (req, res) => {
  try {
    // Try to get capacity data from Supabase
    try {
      const { data: labs, error: labsError } = await supabase
        .from('labs')
        .select('id, name, capacity');

      if (labsError) {
        throw labsError;
      }

      const { data: computers, error: computersError } = await supabase
        .from('computer_inventory')
        .select('lab_id, status');

      if (computersError) {
        throw computersError;
      }

      // Calculate capacity data
      const capacityData = {
        labs: labs.map(lab => {
          const labComputers = computers.filter(comp => comp.lab_id === lab.id);
          const totalComputers = labComputers.length;
          const availableComputers = labComputers.filter(comp => comp.status === 'available').length;
          const occupiedComputers = labComputers.filter(comp => comp.status === 'occupied').length;
          const maintenanceComputers = labComputers.filter(comp => comp.status === 'maintenance').length;

          return {
            id: lab.id,
            name: lab.name,
            total_computers: totalComputers,
            available_computers: availableComputers,
            occupied_computers: occupiedComputers,
            maintenance_computers: maintenanceComputers,
            capacity_percentage: totalComputers > 0 ? Math.round((occupiedComputers / totalComputers) * 100) : 0
          };
        })
      };

      res.json({
        message: 'Capacity data retrieved successfully',
        data: capacityData
      });

    } catch (supabaseError) {
      // Supabase capacity fetch failed, using sample data
      
      // Provide sample capacity data
      const capacityData = {
        labs: [
          {
            id: 'f202a2b2-08b0-41cf-8f97-c0160f247ad8',
            name: 'Computer Lab 1',
            total_computers: 15,
            available_computers: 5,
            occupied_computers: 10,
            maintenance_computers: 0,
            capacity_percentage: 67
          },
          {
            id: 'a8f3d1e5-2b4c-4d6e-8f9a-1c2d3e4f5a6b',
            name: 'Computer Lab 2',
            total_computers: 19,
            available_computers: 8,
            occupied_computers: 11,
            maintenance_computers: 0,
            capacity_percentage: 58
          }
        ]
      };

      res.json({
        message: 'Capacity data retrieved successfully (sample data)',
        data: capacityData
      });
    }

  } catch (error) {
    console.error('Get capacity data error:', error);
    res.status(500).json({ error: 'Failed to fetch capacity data' });
  }
});

// Get class-lab assignments (MUST come before /:labId route)
router.get('/lab-assignments', authenticateToken, async (req, res) => {
  try {
    const { data: assignments, error } = await supabase
      .from('class_lab_assignments')
      .select('id, class_id, lab_id, assigned_at, is_active')
      .eq('is_active', true);

    if (error) {
      // Return empty assignments if table doesn't exist or other error
      return res.json({ assignments: {} });
    }

    // Transform to the format expected by frontend: { labId: [classId1, classId2, ...] }
    const assignmentMap = {};
    if (assignments && assignments.length > 0) {
      assignments.forEach(assignment => {
        if (!assignmentMap[assignment.lab_id]) {
          assignmentMap[assignment.lab_id] = [];
        }
        assignmentMap[assignment.lab_id].push(assignment.class_id);
      });
    }

    res.json({ assignments: assignmentMap });

  } catch (error) {
    res.json({ assignments: {} }); // Return empty assignments on error
  }
});

// Debug endpoint to check specific class enrollment data
router.get('/debug-class/:classId', authenticateToken, async (req, res) => {
  try {
    const { classId } = req.params;
    console.log(`🔍 Debug: Checking data for class ID: ${classId}`);

    // Get the class details
    const { data: classData, error: classError } = await supabase
      .from('classes')
      .select('*')
      .eq('id', classId);

    // Get enrollments for this specific class
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        users!inner(id, student_id, email, first_name, last_name, role, is_active),
        classes!inner(id, name)
      `)
      .eq('class_id', classId);

    // Get all enrollments to see the structure
    const { data: allEnrollments, error: allEnrollmentError } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        users!inner(id, student_id, email, first_name, last_name, role, is_active),
        classes!inner(id, name)
      `);

    res.json({
      classId,
      classData: classData || [],
      classError,
      enrollments: enrollments || [],
      enrollmentError,
      allEnrollments: allEnrollments || [],
      allEnrollmentError,
      enrollmentCount: enrollments?.length || 0,
      totalEnrollments: allEnrollments?.length || 0
    });

  } catch (error) {
    console.error('Debug class error:', error);
    res.status(500).json({ error: 'Failed to fetch debug data', details: error.message });
  }
});

// Debug endpoint for specific class ID issue
router.get('/debug-specific-class/:classId', authenticateToken, async (req, res) => {
  try {
    const { classId } = req.params;
    console.log(`🔍 DEBUGGING SPECIFIC CLASS: ${classId}`);

    // Step 1: Raw enrollment data for this class
    const { data: rawEnrollments, error: rawError } = await supabase
      .from('student_enrollments')
      .select('*')
      .eq('class_id', classId);

    // Step 2: Enrollment data with user join (exactly like the API)
    const { data: enrollmentWithUsers, error: joinError } = await supabase
      .from('student_enrollments')
      .select(`
        student_id,
        users!inner(id, student_id, email, first_name, last_name)
      `)
      .eq('class_id', classId)
      .eq('is_active', true)
      .eq('users.role', 'student')
      .eq('users.is_active', true);

    // Step 3: Check users table for the student IDs from enrollments
    let userDetails = [];
    if (rawEnrollments && rawEnrollments.length > 0) {
      const studentIds = rawEnrollments.map(e => e.student_id);
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('*')
        .in('id', studentIds);
      userDetails = users || [];
    }

    // Step 4: Check class details
    const { data: classDetails, error: classError } = await supabase
      .from('classes')
      .select('*')
      .eq('id', classId);

    res.json({
      classId,
      step1_raw_enrollments: {
        count: rawEnrollments?.length || 0,
        data: rawEnrollments,
        error: rawError
      },
      step2_enrollment_with_users: {
        count: enrollmentWithUsers?.length || 0,
        data: enrollmentWithUsers,
        error: joinError
      },
      step3_user_details: {
        count: userDetails.length,
        data: userDetails
      },
      step4_class_details: {
        data: classDetails,
        error: classError
      },
      analysis: {
        hasRawEnrollments: (rawEnrollments?.length || 0) > 0,
        hasJoinedData: (enrollmentWithUsers?.length || 0) > 0,
        possibleIssue: (rawEnrollments?.length || 0) > 0 && (enrollmentWithUsers?.length || 0) === 0 ?
          'JOIN query failing - check user data integrity' :
          'Unknown issue'
      }
    });

  } catch (error) {
    console.error('Debug specific class error:', error);
    res.status(500).json({ error: 'Failed to debug specific class', details: error.message });
  }
});

// Debug endpoint to check why assign student modal is empty
router.get('/debug-assign-issue/:classId/:labId', authenticateToken, async (req, res) => {
  try {
    const { classId, labId } = req.params;
    console.log(`🔍 DEBUGGING ASSIGN ISSUE - Class: ${classId}, Lab: ${labId}`);

    // Step 1: Check if class exists
    const { data: classData, error: classError } = await supabase
      .from('classes')
      .select('*')
      .eq('id', classId);

    // Step 2: Check if lab exists
    const { data: labData, error: labError } = await supabase
      .from('labs')
      .select('*')
      .eq('id', labId);

    // Step 3: Check student_enrollments table structure and data
    const { data: enrollmentTableInfo, error: enrollmentTableError } = await supabase
      .from('student_enrollments')
      .select('*')
      .limit(5);

    // Step 4: Check specific enrollments for this class
    const { data: classEnrollments, error: classEnrollmentError } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        users!inner(id, student_id, email, first_name, last_name, role, is_active)
      `)
      .eq('class_id', classId)
      .eq('is_active', true);

    // Step 5: Check all students
    const { data: allStudents, error: allStudentsError } = await supabase
      .from('users')
      .select('id, student_id, email, first_name, last_name, role, is_active')
      .eq('role', 'student')
      .eq('is_active', true);

    // Step 6: Check seat assignments for this lab
    const { data: seatAssignments, error: seatAssignmentError } = await supabase
      .from('seat_assignments')
      .select('*')
      .eq('lab_id', labId);

    // Step 7: Simulate the exact query from unassigned-students endpoint
    let simulatedResult = [];
    let simulatedError = null;

    if (!classEnrollmentError && classEnrollments) {
      const enrolledStudents = classEnrollments.map(enrollment => ({
        id: enrollment.users.id,
        student_id: enrollment.users.student_id,
        email: enrollment.users.email,
        first_name: enrollment.users.first_name,
        last_name: enrollment.users.last_name
      }));

      if (!seatAssignmentError && seatAssignments) {
        const assignedStudentIds = seatAssignments.map(assignment => assignment.student_id) || [];
        simulatedResult = enrolledStudents.filter(student =>
          !assignedStudentIds.includes(student.id)
        );
      } else {
        simulatedResult = enrolledStudents;
      }
    }

    res.json({
      debug: {
        classId,
        labId,
        timestamp: new Date().toISOString()
      },
      step1_class: {
        exists: !!classData?.length,
        data: classData,
        error: classError
      },
      step2_lab: {
        exists: !!labData?.length,
        data: labData,
        error: labError
      },
      step3_enrollment_table: {
        exists: !enrollmentTableError,
        sampleData: enrollmentTableInfo,
        error: enrollmentTableError
      },
      step4_class_enrollments: {
        count: classEnrollments?.length || 0,
        data: classEnrollments,
        error: classEnrollmentError
      },
      step5_all_students: {
        count: allStudents?.length || 0,
        data: allStudents,
        error: allStudentsError
      },
      step6_seat_assignments: {
        count: seatAssignments?.length || 0,
        data: seatAssignments,
        error: seatAssignmentError
      },
      step7_simulated_result: {
        count: simulatedResult.length,
        data: simulatedResult,
        error: simulatedError
      },
      conclusion: {
        shouldShowStudents: simulatedResult.length > 0,
        reason: simulatedResult.length === 0 ?
          (classEnrollments?.length === 0 ? 'No students enrolled in this class' : 'All students already assigned seats') :
          'Students should appear in modal'
      }
    });

  } catch (error) {
    console.error('Debug assign issue error:', error);
    res.status(500).json({ error: 'Failed to debug assign issue', details: error.message });
  }
});

// Quick fix endpoint to enroll students based on email patterns
router.post('/quick-enroll-students', authenticateToken, async (req, res) => {
  try {
    console.log('🔧 Quick enrolling students based on email patterns...');

    // Get all classes
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name');

    if (classesError) {
      return res.status(500).json({ error: 'Failed to fetch classes', details: classesError });
    }

    // Get all students
    const { data: students, error: studentsError } = await supabase
      .from('users')
      .select('id, email, first_name, last_name')
      .eq('role', 'student')
      .eq('is_active', true);

    if (studentsError) {
      return res.status(500).json({ error: 'Failed to fetch students', details: studentsError });
    }

    const enrollments = [];

    // Map email patterns to class names
    const patterns = [
      { emailPattern: '11coma_', classPattern: '11 COM A' },
      { emailPattern: '11comb_', classPattern: '11 COM B' },
      { emailPattern: '11nma_', classPattern: '11 NM A' },
      { emailPattern: '11nmb_', classPattern: '11 NM B' },
      { emailPattern: '11nmc_', classPattern: '11 NM C' },
      { emailPattern: '11nmd_', classPattern: '11 NM D' },
      { emailPattern: '12coma_', classPattern: '12 COM A' },
      { emailPattern: '12comb_', classPattern: '12 COM B' },
      { emailPattern: '12ma_', classPattern: '12 M A' },
      { emailPattern: '12meda_', classPattern: '12 MED A' },
      { emailPattern: '12nme_', classPattern: '12 NM E' },
      { emailPattern: '12nmb_', classPattern: '12 NM B' }
    ];

    for (const pattern of patterns) {
      // Find matching students
      const matchingStudents = students.filter(student =>
        student.email.includes(pattern.emailPattern)
      );

      // Find matching class
      const matchingClass = classes.find(cls =>
        cls.name.includes(pattern.classPattern) ||
        cls.name.toLowerCase().includes(pattern.classPattern.toLowerCase())
      );

      if (matchingClass && matchingStudents.length > 0) {
        console.log(`📝 Found ${matchingStudents.length} students for ${pattern.classPattern} (${matchingClass.name})`);

        for (const student of matchingStudents) {
          enrollments.push({
            student_id: student.id,
            class_id: matchingClass.id,
            academic_year: '2024-25',
            is_active: true,
            notes: `Auto-enrolled based on email pattern: ${pattern.emailPattern}`
          });
        }
      }
    }

    // Insert enrollments
    if (enrollments.length > 0) {
      const { data: insertedEnrollments, error: insertError } = await supabase
        .from('student_enrollments')
        .upsert(enrollments, {
          onConflict: 'student_id,class_id,academic_year',
          ignoreDuplicates: true
        });

      if (insertError) {
        console.error('Insert error:', insertError);
        return res.status(500).json({ error: 'Failed to insert enrollments', details: insertError });
      }

      console.log(`✅ Successfully enrolled ${enrollments.length} students`);

      res.json({
        message: 'Students enrolled successfully',
        enrollments: enrollments.length,
        patterns: patterns.length,
        details: enrollments
      });
    } else {
      res.json({
        message: 'No matching students found for enrollment',
        enrollments: 0,
        availableStudents: students.length,
        availableClasses: classes.length
      });
    }

  } catch (error) {
    console.error('Quick enroll error:', error);
    res.status(500).json({ error: 'Failed to enroll students', details: error.message });
  }
});

// Debug endpoint to check actual demo students and classes
router.get('/debug-demo-data', authenticateToken, async (req, res) => {
  try {
    // Get all demo students
    const { data: demoStudents, error: studentsError } = await supabase
      .from('users')
      .select('id, student_id, email, first_name, last_name, role, is_active')
      .eq('role', 'student')
      .eq('is_active', true)
      .ilike('email', '%student%');

    // Get all classes
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name, is_active')
      .eq('is_active', true);

    // Get current enrollments
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .select(`
        id,
        student_id,
        class_id,
        academic_year,
        users!inner(email, first_name, last_name),
        classes!inner(name)
      `);

    res.json({
      demoStudents: demoStudents || [],
      studentsError,
      classes: classes || [],
      classesError,
      enrollments: enrollments || [],
      enrollmentError,
      studentCount: demoStudents?.length || 0,
      classCount: classes?.length || 0,
      enrollmentCount: enrollments?.length || 0
    });

  } catch (error) {
    console.error('Debug demo data error:', error);
    res.status(500).json({ error: 'Failed to fetch debug data', details: error.message });
  }
});

// Debug endpoint to check enrollment data
router.get('/debug-enrollments', authenticateToken, async (req, res) => {
  try {
    // Get all enrollments
    const { data: enrollments, error: enrollmentsError } = await supabase
      .from('student_enrollments')
      .select(`
        id,
        student_id,
        class_id,
        academic_year,
        is_active,
        users!inner(id, student_id, email, first_name, last_name, role),
        classes!inner(id, name)
      `);

    // Get all classes
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name');

    // Get all students
    const { data: students, error: studentsError } = await supabase
      .from('users')
      .select('id, student_id, email, first_name, last_name, role')
      .eq('role', 'student');

    res.json({
      enrollments: enrollments || [],
      enrollmentsError,
      classes: classes || [],
      classesError,
      students: students || [],
      studentsError,
      enrollmentCount: enrollments?.length || 0,
      classCount: classes?.length || 0,
      studentCount: students?.length || 0
    });

  } catch (error) {
    console.error('Debug enrollments error:', error);
    res.status(500).json({ error: 'Failed to fetch debug data', details: error.message });
  }
});

// Database diagnostic endpoint (MUST come before parameterized routes)
router.get('/database-diagnostic', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 Running database diagnostic...');

    // Test 1: Try a simple select from seat_assignments
    console.log('📋 Test 1: Simple select test...');
    const { data: selectTest, error: selectError } = await supabase
      .from('seat_assignments')
      .select('*')
      .limit(1);

    console.log('Select test result:', { selectTest, selectError });

    // Test 2: Check foreign key references
    console.log('📋 Test 2: Checking foreign key references...');

    // Check if student exists
    const { data: studentCheck, error: studentError } = await supabase
      .from('users')
      .select('id, student_id, first_name, last_name')
      .eq('id', 'f65f33ed-3771-4883-8866-bad0f0b394c5')
      .single();

    console.log('Student check:', { studentCheck, studentError });

    // Check if lab exists
    const { data: labCheck, error: labError } = await supabase
      .from('labs')
      .select('id, name')
      .eq('id', 'f202a2b2-08b0-41cf-8f97-c0160f247ad8')
      .single();

    console.log('Lab check:', { labCheck, labError });

    // Check if class exists
    const { data: classCheck, error: classError } = await supabase
      .from('classes')
      .select('id, name')
      .eq('id', 'e519c46b-7380-4ab4-9529-6bc258edbb8d')
      .single();

    console.log('Class check:', { classCheck, classError });

    // Test 3: Try minimal insert
    console.log('📋 Test 3: Minimal insert test...');
    const testInsertData = {
      student_id: 'f65f33ed-3771-4883-8866-bad0f0b394c5',
      seat_id: 'TEST-DIAGNOSTIC-001',
      lab_id: 'f202a2b2-08b0-41cf-8f97-c0160f247ad8',
      is_active: true
    };

    const { data: insertTest, error: insertTestError } = await supabase
      .from('seat_assignments')
      .insert([testInsertData])
      .select('*');

    console.log('Insert test result:', { insertTest, insertTestError });

    // Clean up test record if successful
    if (insertTest && insertTest.length > 0) {
      console.log('🧹 Cleaning up test record...');
      await supabase
        .from('seat_assignments')
        .delete()
        .eq('id', insertTest[0].id);
    }

    res.json({
      message: 'Database diagnostic completed',
      results: {
        selectTest: { data: selectTest, error: selectError },
        foreignKeys: {
          student: { data: studentCheck, error: studentError },
          lab: { data: labCheck, error: labError },
          class: { data: classCheck, error: classError }
        },
        insertTest: { data: insertTest, error: insertTestError }
      }
    });

  } catch (error) {
    console.error('❌ Database diagnostic error:', error);
    res.status(500).json({
      error: 'Database diagnostic failed',
      details: error.message,
      stack: error.stack
    });
  }
});

// Get unassigned students for a class and lab (MUST come before /:labId route)
router.get('/unassigned-students/:classId/:labId', authenticateToken, async (req, res) => {
  try {
    const { classId, labId } = req.params;
    console.log(`🔍 REAL CALL - Fetching unassigned students for class: ${classId}, lab: ${labId}`);

    // Get students enrolled in the specific class
    // Fix: Use manual join to avoid relationship ambiguity

    // First get enrollments for this class
    const { data: enrollments, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .select('student_id')
      .eq('class_id', classId)
      .eq('is_active', true);

    if (enrollmentError) {
      console.error('Error fetching enrollments:', enrollmentError);
      return res.json({
        message: 'No enrolled students found for this class (enrollment error)',
        students: [],
        total: 0,
        classId: classId,
        labId: labId,
        error: enrollmentError
      });
    }

    if (!enrollments || enrollments.length === 0) {
      console.log('No enrollments found for this class');
      return res.json({
        message: 'No students enrolled in this class',
        students: [],
        total: 0,
        classId: classId,
        labId: labId
      });
    }

    // Get user details for enrolled students
    const studentIds = enrollments.map(e => e.student_id);
    const { data: students, error: studentsError } = await supabase
      .from('users')
      .select('id, student_id, email, first_name, last_name')
      .in('id', studentIds)
      .eq('role', 'student')
      .eq('is_active', true);

    console.log(`📊 REAL CALL - Found ${students?.length || 0} enrolled students`);
    console.log('REAL CALL - Students data:', students);
    console.log('REAL CALL - Students error:', studentsError);

    // Additional debug info
    console.log('REAL CALL - Query parameters:');
    console.log('  - classId:', classId);
    console.log('  - labId:', labId);
    console.log('  - scheduleId from query:', req.query.scheduleId);
    console.log('  - enrollments found:', enrollments?.length || 0);

    if (studentsError) {
      console.error('Error fetching user details for enrolled students:', studentsError);
      return res.json({
        message: 'Error fetching student details',
        students: [],
        total: 0,
        classId: classId,
        labId: labId,
        error: studentsError
      });
    }

    // Students data is already in the correct format from users table
    const enrolledStudents = students || [];

    // Get seat assignments for this lab to filter out already assigned students
    const { data: seatAssignments, error: seatError } = await supabase
      .from('seat_assignments')
      .select('student_id')
      .eq('lab_id', labId)
      .eq('is_active', true);

    if (seatError) {
      // If seat_assignments table doesn't exist, return all enrolled students as unassigned
      return res.json({
        message: 'Unassigned students retrieved successfully',
        students: enrolledStudents,
        total: enrolledStudents.length
      });
    }

    // Filter out students who are already assigned to seats in this lab
    const assignedStudentIds = seatAssignments?.map(assignment => assignment.student_id) || [];
    const unassignedStudents = enrolledStudents.filter(student =>
      !assignedStudentIds.includes(student.id)
    );

    console.log('REAL CALL - Final result:');
    console.log('  - Enrolled students:', enrolledStudents.length);
    console.log('  - Assigned students in lab:', assignedStudentIds.length);
    console.log('  - Unassigned students:', unassignedStudents.length);
    console.log('  - Unassigned students data:', unassignedStudents);

    res.json({
      message: 'Unassigned students retrieved successfully',
      students: unassignedStudents,
      total: unassignedStudents.length,
      classId: classId,
      labId: labId,
      debug: {
        enrolledCount: enrolledStudents.length,
        assignedInLabCount: assignedStudentIds.length,
        unassignedCount: unassignedStudents.length
      }
    });

  } catch (error) {
    console.error('Get unassigned students error:', error);
    res.status(500).json({ error: 'Failed to fetch unassigned students' });
  }
});

// Get capacity data for a specific lab
router.get('/:labId', authenticateToken, async (req, res) => {
  try {
    const { labId } = req.params;

    // Try to get specific lab capacity from Supabase
    try {
      const { data: lab, error: labError } = await supabase
        .from('labs')
        .select('id, name, capacity')
        .eq('id', labId)
        .single();

      if (labError) {
        throw labError;
      }

      const { data: computers, error: computersError } = await supabase
        .from('computer_inventory')
        .select('id, computer_id, status')
        .eq('lab_id', labId);

      if (computersError) {
        throw computersError;
      }

      const totalComputers = computers.length;
      const availableComputers = computers.filter(comp => comp.status === 'available').length;
      const occupiedComputers = computers.filter(comp => comp.status === 'occupied').length;
      const maintenanceComputers = computers.filter(comp => comp.status === 'maintenance').length;

      const capacityData = {
        id: lab.id,
        name: lab.name,
        total_computers: totalComputers,
        available_computers: availableComputers,
        occupied_computers: occupiedComputers,
        maintenance_computers: maintenanceComputers,
        capacity_percentage: totalComputers > 0 ? Math.round((occupiedComputers / totalComputers) * 100) : 0,
        computers: computers
      };

      res.json({
        message: 'Lab capacity data retrieved successfully',
        data: capacityData
      });

    } catch (supabaseError) {
      // Supabase lab capacity fetch failed, using sample data
      
      // Provide sample lab capacity data
      let sampleData;
      if (labId === 'f202a2b2-08b0-41cf-8f97-c0160f247ad8') {
        sampleData = {
          id: 'f202a2b2-08b0-41cf-8f97-c0160f247ad8',
          name: 'Computer Lab 1',
          total_computers: 15,
          available_computers: 5,
          occupied_computers: 10,
          maintenance_computers: 0,
          capacity_percentage: 67,
          computers: Array.from({ length: 15 }, (_, i) => ({
            id: `comp-cl1-${i + 1}`,
            computer_id: `CL1-PC-${String(i + 1).padStart(3, '0')}`,
            status: i < 10 ? 'occupied' : 'available'
          }))
        };
      } else if (labId === 'a8f3d1e5-2b4c-4d6e-8f9a-1c2d3e4f5a6b') {
        sampleData = {
          id: 'a8f3d1e5-2b4c-4d6e-8f9a-1c2d3e4f5a6b',
          name: 'Computer Lab 2',
          total_computers: 19,
          available_computers: 8,
          occupied_computers: 11,
          maintenance_computers: 0,
          capacity_percentage: 58,
          computers: Array.from({ length: 19 }, (_, i) => ({
            id: `comp-cl2-${i + 1}`,
            computer_id: `CL2-PC-${String(i + 1).padStart(3, '0')}`,
            status: i < 11 ? 'occupied' : 'available'
          }))
        };
      } else {
        sampleData = {
          id: labId,
          name: 'Demo Lab',
          total_computers: 20,
          available_computers: 10,
          occupied_computers: 10,
          maintenance_computers: 0,
          capacity_percentage: 50,
          computers: []
        };
      }

      res.json({
        message: 'Lab capacity data retrieved successfully (sample data)',
        data: sampleData
      });
    }

  } catch (error) {
    console.error('Get lab capacity data error:', error);
    res.status(500).json({ error: 'Failed to fetch lab capacity data' });
  }
});

// Get student capacity data
router.get('/student/:studentId', authenticateToken, async (req, res) => {
  try {
    const { studentId } = req.params;

    // Return placeholder data for now
    res.json({
      message: 'Student capacity data retrieved successfully',
      data: {
        student_id: studentId,
        assigned_lab: null,
        assigned_seat: null,
        current_session: null
      }
    });

  } catch (error) {
    console.error('Get student capacity data error:', error);
    res.status(500).json({ error: 'Failed to fetch student capacity data' });
  }
});

// Get lab seat assignments
router.get('/labs/:labId/seat-assignments', authenticateToken, async (req, res) => {
  try {
    const { labId } = req.params;
    const { scheduleId, classId } = req.query;

    console.log(`🪑 Fetching seat assignments for lab: ${labId}, schedule: ${scheduleId}, class: ${classId}`);

    // Build query for seat assignments
    let query = supabase
      .from('seat_assignments')
      .select(`
        id,
        student_id,
        seat_id,
        lab_id,
        class_id,
        schedule_id,
        assigned_at,
        is_active,
        notes,
        users!inner(id, first_name, last_name, student_id, email)
      `)
      .eq('lab_id', labId)
      .eq('is_active', true);

    // If class ID is provided, filter by it (most important filter)
    if (classId) {
      query = query.eq('class_id', classId);
      console.log(`🔍 Filtering assignments by class: ${classId}`);
    }

    // If schedule ID is provided, filter by it
    if (scheduleId) {
      query = query.eq('schedule_id', scheduleId);
      console.log(`🔍 Filtering assignments by schedule: ${scheduleId}`);
    }

    const { data: assignments, error } = await query;

    console.log('Seat assignments query result:', { assignments, error });

    if (error) {
      console.error('Error fetching seat assignments:', error);
      return res.status(500).json({
        error: 'Failed to fetch seat assignments',
        details: error
      });
    }

    // Transform the data to match frontend expectations
    const transformedAssignments = assignments ? assignments.map(assignment => ({
      id: assignment.id,
      student_id: assignment.student_id,
      seat_id: assignment.seat_id,
      lab_id: assignment.lab_id,
      class_id: assignment.class_id,
      schedule_id: assignment.schedule_id,
      assigned_at: assignment.assigned_at,
      is_active: assignment.is_active,
      notes: assignment.notes,
      student: {
        id: assignment.users.id,
        first_name: assignment.users.first_name,
        last_name: assignment.users.last_name,
        student_id: assignment.users.student_id,
        email: assignment.users.email
      }
    })) : [];

    console.log(`✅ Found ${transformedAssignments.length} seat assignments`);

    res.json({
      message: 'Lab seat assignments retrieved successfully',
      data: {
        lab_id: labId,
        schedule_id: scheduleId,
        assignments: transformedAssignments,
        total_assignments: transformedAssignments.length
      }
    });

  } catch (error) {
    console.error('Error fetching lab seat assignments:', error);
    res.status(500).json({ error: 'Failed to fetch lab seat assignments' });
  }
});

// Create class_lab_assignments table if it doesn't exist
router.post('/create-assignments-table', authenticateToken, async (req, res) => {
  try {
    // Create the table using raw SQL
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS class_lab_assignments (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        class_id UUID NOT NULL,
        lab_id UUID NOT NULL,
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        assigned_by VARCHAR(100) DEFAULT 'system',
        is_active BOOLEAN DEFAULT true,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(class_id)
      );

      CREATE INDEX IF NOT EXISTS idx_class_lab_assignments_class_id ON class_lab_assignments(class_id);
      CREATE INDEX IF NOT EXISTS idx_class_lab_assignments_lab_id ON class_lab_assignments(lab_id);
      CREATE INDEX IF NOT EXISTS idx_class_lab_assignments_active ON class_lab_assignments(is_active);
    `;

    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });

    if (error) {
      console.error('Error creating table:', error);
      return res.status(500).json({ error: 'Failed to create table', details: error.message });
    }

    res.json({ message: 'Table created successfully' });
  } catch (error) {
    console.error('Create table error:', error);
    res.status(500).json({ error: 'Failed to create table' });
  }
});

// Save class-lab assignments
router.post('/lab-assignments', authenticateToken, async (req, res) => {
  try {
    const { assignments } = req.body; // assignments is an object: { labId: [classId1, classId2, ...] }

    if (!assignments || typeof assignments !== 'object') {
      return res.status(400).json({ error: 'Invalid assignments data' });
    }

    // For now, we'll store assignments in memory/local storage since table creation is complex
    // In a production environment, the table should be created via database migrations

    // Clear all existing assignments first (if table exists)
    try {
      const { error: deleteError } = await supabase
        .from('class_lab_assignments')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

      if (deleteError && !deleteError.message.includes('does not exist')) {
        console.error('Error clearing existing assignments:', deleteError);
      }
    } catch (deleteErr) {
      // Table might not exist yet, continue
    }

    // Insert new assignments (if table exists)
    const assignmentRecords = [];
    for (const [labId, classIds] of Object.entries(assignments)) {
      if (Array.isArray(classIds)) {
        for (const classId of classIds) {
          assignmentRecords.push({
            class_id: classId,
            lab_id: labId,
            assigned_by: req.user?.email || 'system',
            notes: 'Assigned via capacity planning interface'
          });
        }
      }
    }

    if (assignmentRecords.length > 0) {
      try {
        const { data: insertedData, error: insertError } = await supabase
          .from('class_lab_assignments')
          .insert(assignmentRecords);

        if (insertError) {
          console.error('Error inserting assignments:', insertError);
          return res.status(500).json({ error: 'Failed to save assignments to database', details: insertError.message });
        }
      } catch (insertErr) {
        console.error('Insert error:', insertErr);
        return res.status(500).json({ error: 'Failed to save assignments', details: insertErr.message });
      }
    }

    res.json({
      message: 'Lab assignments saved successfully',
      assignmentsCount: assignmentRecords.length,
      note: 'Assignments are currently stored in application state. Database table creation required for persistence.'
    });

  } catch (error) {
    console.error('Save lab assignments error:', error);
    res.status(500).json({ error: 'Failed to save lab assignments' });
  }
});

// Get students and groups for capacity management
router.get('/students-groups/:classId', authenticateToken, async (req, res) => {
  try {
    const { classId } = req.params;

    // Try to get real data from Supabase first
    try {
      // Get groups for this class
      const { data: groups, error: groupsError } = await supabase
        .from('groups')
        .select(`
          id, name, class_id, max_members, member_count, leader_name, description
        `)
        .eq('class_id', classId);

      // Get group members separately
      let groupMembers = [];
      if (!groupsError && groups && groups.length > 0) {
        const groupIds = groups.map(g => g.id);
        const { data: members, error: membersError } = await supabase
          .from('group_members')
          .select(`
            id, group_id, user_id, role,
            users!inner(id, first_name, last_name, student_id)
          `)
          .in('group_id', groupIds);

        if (!membersError && members) {
          groupMembers = members;
        }
      }

      // Get students in this class
      const { data: students, error: studentsError } = await supabase
        .from('users')
        .select('id, first_name, last_name, student_id, email')
        .eq('role', 'student')
        .not('student_id', 'is', null);

      if (!groupsError && groups) {
        // Map the data to match frontend expectations
        const mappedGroups = groups.map(group => {
          const groupMembersList = groupMembers
            .filter(member => member.group_id === group.id)
            .map(member => ({
              id: member.users.id,
              first_name: member.users.first_name,
              last_name: member.users.last_name,
              student_id: member.users.student_id,
              role: member.role
            }));

          return {
            id: group.id,
            name: group.name,
            class_id: group.class_id,
            max_members: group.max_members,
            member_count: group.member_count,
            leader_name: group.leader_name,
            description: group.description,
            members: groupMembersList
          };
        });

        return res.json({
          message: 'Students and groups retrieved successfully',
          data: {
            class_id: classId,
            students: students || [],
            groups: mappedGroups,
            total_students: students ? students.length : 0,
            total_groups: mappedGroups.length
          }
        });
      }
    } catch (supabaseError) {
      // Supabase groups fetch failed, using sample data
    }

    // Return sample data for demo
    const sampleGroups = [
      {
        id: 'group-1',
        name: 'Group Alpha',
        class_id: classId,
        max_members: 4,
        member_count: 3,
        leader_name: 'John Doe',
        description: 'Sample group for testing',
        members: [
          { id: 'student-1', first_name: 'John', last_name: 'Doe', student_id: 'ST001', role: 'leader' },
          { id: 'student-2', first_name: 'Jane', last_name: 'Smith', student_id: 'ST002', role: 'member' },
          { id: 'student-3', first_name: 'Bob', last_name: 'Johnson', student_id: 'ST003', role: 'member' }
        ]
      },
      {
        id: 'group-2',
        name: 'Group Beta',
        class_id: classId,
        max_members: 4,
        member_count: 2,
        leader_name: 'Alice Brown',
        description: 'Another sample group',
        members: [
          { id: 'student-4', first_name: 'Alice', last_name: 'Brown', student_id: 'ST004', role: 'leader' },
          { id: 'student-5', first_name: 'Charlie', last_name: 'Wilson', student_id: 'ST005', role: 'member' }
        ]
      }
    ];

    res.json({
      message: 'Students and groups retrieved successfully (sample data)',
      data: {
        class_id: classId,
        students: [],
        groups: sampleGroups,
        total_students: 0,
        total_groups: sampleGroups.length
      }
    });

  } catch (error) {
    console.error('Get students and groups error:', error);
    res.status(500).json({ error: 'Failed to fetch students and groups' });
  }
});

// Create seat assignment (simplified version)
router.post('/seat-assignments', authenticateToken, async (req, res) => {
  try {
    console.log('🪑 Seat assignment request received');
    console.log('Request body:', req.body);

    const { student_id, seat_id, lab_id, class_id, schedule_id } = req.body;

    console.log('🪑 Creating seat assignment:', {
      student_id,
      seat_id,
      lab_id,
      class_id,
      schedule_id
    });

    // Validate required fields
    if (!student_id || !seat_id || !lab_id) {
      console.log('❌ Missing required fields:', { student_id, seat_id, lab_id });
      return res.status(400).json({
        error: 'Missing required fields: student_id, seat_id, lab_id are required'
      });
    }

    console.log('✅ All required fields present, proceeding with assignment...');

    // Check if student is already assigned to a seat in this lab
    console.log('🔍 Checking if student already has a seat in this lab...');
    const { data: existingStudentAssignment, error: studentCheckError } = await supabase
      .from('seat_assignments')
      .select('*')
      .eq('student_id', student_id)
      .eq('lab_id', lab_id)
      .eq('is_active', true);

    console.log('Student assignment check:', { existingStudentAssignment, studentCheckError });

    if (studentCheckError) {
      console.error('❌ Error checking student assignment:', studentCheckError);
      return res.status(500).json({
        error: 'Failed to check student assignment',
        details: studentCheckError
      });
    }

    if (existingStudentAssignment && existingStudentAssignment.length > 0) {
      console.log('❌ Student already has a seat in this lab:', existingStudentAssignment[0]);
      return res.status(409).json({
        error: 'Student is already assigned to a seat in this lab',
        existing_assignment: existingStudentAssignment[0],
        message: `Student is already assigned to seat ${existingStudentAssignment[0].seat_id} in this lab`
      });
    }

    // Check if seat is already assigned to another student in the same class
    console.log('🔍 Checking if seat is already assigned in this class...');
    const { data: existingSeatAssignment, error: seatCheckError } = await supabase
      .from('seat_assignments')
      .select('*')
      .eq('seat_id', seat_id)
      .eq('class_id', class_id)  // Only check within the same class
      .eq('is_active', true);

    console.log('Seat assignment check:', { existingSeatAssignment, seatCheckError });

    if (seatCheckError) {
      console.error('❌ Error checking seat assignment:', seatCheckError);
      return res.status(500).json({
        error: 'Failed to check seat availability',
        details: seatCheckError
      });
    }

    if (existingSeatAssignment && existingSeatAssignment.length > 0) {
      console.log('❌ Seat already assigned in this class:', existingSeatAssignment[0]);
      return res.status(409).json({
        error: 'Seat is already assigned to another student in this class',
        existing_assignment: existingSeatAssignment[0],
        message: `Seat ${seat_id} is already assigned to another student in this class`
      });
    }

    console.log('✅ No conflicts found, creating assignment...');

    // Create the seat assignment
    const assignmentData = {
      student_id,
      seat_id,
      lab_id,
      class_id: class_id || null,
      schedule_id: schedule_id || null,
      assigned_at: new Date().toISOString(),
      is_active: true,
      notes: 'Assigned via capacity planning'
    };

    console.log('📝 Assignment data to insert:', assignmentData);
    console.log('📝 Data types:', {
      student_id: typeof assignmentData.student_id,
      seat_id: typeof assignmentData.seat_id,
      lab_id: typeof assignmentData.lab_id,
      class_id: typeof assignmentData.class_id,
      schedule_id: typeof assignmentData.schedule_id,
      assigned_at: typeof assignmentData.assigned_at,
      is_active: typeof assignmentData.is_active
    });

    // Verify foreign key references before insert
    console.log('🔍 Verifying foreign key references...');

    // Check student exists
    const { data: studentExists, error: studentExistsError } = await supabase
      .from('users')
      .select('id')
      .eq('id', assignmentData.student_id)
      .single();

    console.log('Student exists check:', { studentExists, studentExistsError });

    if (studentExistsError || !studentExists) {
      console.error('❌ Student not found:', assignmentData.student_id);
      return res.status(400).json({
        error: 'Student not found',
        student_id: assignmentData.student_id,
        details: studentExistsError
      });
    }

    // Check lab exists
    const { data: labExists, error: labExistsError } = await supabase
      .from('labs')
      .select('id')
      .eq('id', assignmentData.lab_id)
      .single();

    console.log('Lab exists check:', { labExists, labExistsError });

    if (labExistsError || !labExists) {
      console.error('❌ Lab not found:', assignmentData.lab_id);
      return res.status(400).json({
        error: 'Lab not found',
        lab_id: assignmentData.lab_id,
        details: labExistsError
      });
    }

    console.log('✅ Foreign key references verified, proceeding with insert...');

    const { data: newAssignment, error: insertError } = await supabase
      .from('seat_assignments')
      .insert([assignmentData])
      .select('*');

    console.log('📊 Detailed insert result:');
    console.log('  - Data:', newAssignment);
    console.log('  - Error:', insertError);
    console.log('  - Error code:', insertError?.code);
    console.log('  - Error message:', insertError?.message);
    console.log('  - Error details:', insertError?.details);
    console.log('  - Error hint:', insertError?.hint);

    if (insertError) {
      console.error('❌ Error creating seat assignment:', insertError);
      return res.status(500).json({
        error: 'Failed to create seat assignment',
        details: insertError,
        step: 'inserting_assignment',
        assignmentData: assignmentData
      });
    }

    console.log('✅ Seat assignment created successfully:', newAssignment[0]);

    res.status(201).json({
      message: 'Seat assigned successfully',
      assignment: newAssignment[0]
    });

  } catch (error) {
    console.error('❌ Exception in seat assignment:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      error: 'Failed to assign seat',
      details: error.message,
      stack: error.stack
    });
  }
});

// Get existing seat assignments for debugging
router.get('/seat-assignments/debug', authenticateToken, async (req, res) => {
  try {
    const { data: assignments, error } = await supabase
      .from('seat_assignments')
      .select(`
        *,
        users!inner(id, first_name, last_name, student_id, email),
        classes!inner(id, name)
      `)
      .eq('is_active', true)
      .order('assigned_at', { ascending: false });

    if (error) {
      return res.status(500).json({ error: error });
    }

    console.log('🔍 Debug: Current seat assignments:', assignments);

    res.json({
      message: 'Active seat assignments retrieved',
      assignments: assignments || [],
      total: assignments ? assignments.length : 0
    });

  } catch (error) {
    console.error('Error fetching seat assignments:', error);
    res.status(500).json({ error: 'Failed to fetch seat assignments' });
  }
});

// Remove seat assignment (for testing/cleanup)
router.delete('/seat-assignments/:assignmentId', authenticateToken, async (req, res) => {
  try {
    const { assignmentId } = req.params;

    console.log('🗑️ Removing seat assignment:', assignmentId);

    const { data: updatedAssignment, error } = await supabase
      .from('seat_assignments')
      .update({
        is_active: false,
        unassigned_at: new Date().toISOString()
      })
      .eq('id', assignmentId)
      .select('*');

    if (error) {
      console.error('Error removing assignment:', error);
      return res.status(500).json({ error: 'Failed to remove assignment', details: error });
    }

    if (!updatedAssignment || updatedAssignment.length === 0) {
      return res.status(404).json({ error: 'Assignment not found' });
    }

    console.log('✅ Assignment removed:', updatedAssignment[0]);

    res.json({
      message: 'Assignment removed successfully',
      assignment: updatedAssignment[0]
    });

  } catch (error) {
    console.error('Error removing assignment:', error);
    res.status(500).json({ error: 'Failed to remove assignment' });
  }
});

// Test endpoint to verify seat assignment functionality
router.post('/test-seat-assignment', authenticateToken, async (req, res) => {
  try {
    console.log('🧪 Testing seat assignment functionality');

    // Test with sample data
    const testData = {
      student_id: 'f65f33ed-3771-4883-8866-bad0f0b394c5', // Demo Student1
      seat_id: 'CL1-CR-001',
      lab_id: 'f202a2b2-08b0-41cf-8f97-c0160f247ad8', // Computer Lab 1
      class_id: 'e519c46b-7380-4ab4-9529-6bc258edbb8d', // 11 NM A
      schedule_id: null
    };

    console.log('Test data:', testData);

    // Try to insert test assignment
    const { data: result, error } = await supabase
      .from('seat_assignments')
      .insert([{
        ...testData,
        assigned_at: new Date().toISOString(),
        is_active: true,
        notes: 'Test assignment'
      }])
      .select('*');

    console.log('Test result:', { result, error });

    if (error) {
      return res.status(500).json({
        success: false,
        error: error,
        message: 'Test assignment failed'
      });
    }

    res.json({
      success: true,
      result: result,
      message: 'Test assignment successful'
    });

  } catch (error) {
    console.error('Test assignment error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Test assignment exception'
    });
  }
});

module.exports = router;
