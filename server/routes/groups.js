const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticateToken, requireRole } = require('../middleware/auth');

const router = express.Router();

// Get available students for a class (not in any group)
router.get('/available-students/:classId', authenticateToken, async (req, res) => {
  try {
    const { classId } = req.params;
    console.log(`🔍 Fetching available students for class: ${classId}`);

    // Get students enrolled in the specific class
    console.log(`📋 Querying student_enrollments table for class: ${classId}`);
    const { data: enrolledStudents, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .select(`
        student_id,
        users!student_enrollments_student_id_fkey(id, student_id, email, first_name, last_name, role, is_active)
      `)
      .eq('class_id', classId)
      .eq('is_active', true)
      .eq('users.role', 'student')
      .eq('users.is_active', true);

    console.log(`📊 Enrollment query result:`, {
      studentsFound: enrolledStudents?.length || 0,
      error: enrollmentError?.message || 'none'
    });

    if (enrollmentError) {
      console.error('Error fetching enrolled students:', enrollmentError);
      console.log('🔍 Student enrollments table may not exist, checking for demo students in the class...');

      // Check if this is a demo/test environment by looking for demo students
      // that might be associated with this class through other means
      const { data: allStudents, error: studentsError } = await supabase
        .from('users')
        .select('id, student_id, email, first_name, last_name, role, is_active')
        .eq('role', 'student')
        .eq('is_active', true);

      if (studentsError) {
        console.error('Error fetching all students:', studentsError);
        return res.status(500).json({ error: 'Failed to fetch students' });
      }

      // For now, return empty array to force proper class-based enrollment
      // This prevents showing all students when class filtering should be enforced
      console.log('⚠️ No student enrollment data found for class, returning empty list');
      console.log('💡 Please ensure students are properly enrolled in classes via the enrollment system');

      return res.json({
        message: 'No students enrolled in this class',
        students: [],
        total: 0,
        classId: classId,
        note: 'Students must be enrolled in classes to appear in group creation'
      });
    }

    // Transform enrolled students data
    const students = enrolledStudents?.map(enrollment => ({
      id: enrollment.users.id,
      student_id: enrollment.users.student_id,
      email: enrollment.users.email,
      first_name: enrollment.users.first_name,
      last_name: enrollment.users.last_name
    })) || [];

    // Get students who are already in groups for this class
    const { data: groupMembers, error: groupError } = await supabase
      .from('group_members')
      .select(`
        user_id,
        groups!inner(class_id)
      `)
      .eq('groups.class_id', classId);

    if (groupError) {
      console.error('Error fetching group members:', groupError);
      // If group_members table doesn't exist, return all enrolled students
      return res.json({
        message: 'Available students retrieved successfully (no groups)',
        students: students,
        total: students.length
      });
    }

    // Filter out students who are already in groups
    const groupedStudentIds = groupMembers?.map(member => member.user_id) || [];
    const availableStudents = students.filter(student =>
      !groupedStudentIds.includes(student.id)
    );

    console.log(`✅ Found ${availableStudents.length} available students out of ${students.length} enrolled`);

    res.json({
      message: 'Available students retrieved successfully',
      students: availableStudents,
      total: availableStudents.length,
      classId: classId
    });

  } catch (error) {
    console.error('Get available students error:', error);
    res.status(500).json({ error: 'Failed to fetch available students', details: error.message });
  }
});

// Debug endpoint to check student enrollments
router.get('/debug/enrollments/:classId', authenticateToken, async (req, res) => {
  try {
    const { classId } = req.params;

    console.log(`🔍 Debug: Checking enrollments for class: ${classId}`);

    // Check if student_enrollments table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('student_enrollments')
      .select('*')
      .limit(1);

    if (tableError) {
      return res.json({
        message: 'Student enrollments table check',
        tableExists: false,
        error: tableError.message,
        suggestion: 'student_enrollments table may not exist or be accessible'
      });
    }

    // Get all enrollments for this class
    const { data: enrollments, error: enrollError } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        users!student_enrollments_student_id_fkey(id, student_id, first_name, last_name, email, role),
        classes!inner(id, name)
      `)
      .eq('class_id', classId);

    // Get all students for comparison
    const { data: allStudents, error: studentsError } = await supabase
      .from('users')
      .select('id, student_id, first_name, last_name, email, role')
      .eq('role', 'student')
      .eq('is_active', true);

    res.json({
      message: 'Debug enrollment data',
      classId: classId,
      tableExists: true,
      enrollments: enrollments || [],
      enrollmentCount: enrollments?.length || 0,
      allStudents: allStudents || [],
      totalStudents: allStudents?.length || 0,
      enrollmentError: enrollError?.message || null,
      studentsError: studentsError?.message || null
    });

  } catch (error) {
    console.error('Debug enrollments error:', error);
    res.status(500).json({ error: 'Debug check failed', details: error.message });
  }
});

// Auto-enroll students in classes based on their student IDs
router.post('/auto-enroll-students', authenticateToken, async (req, res) => {
  try {
    console.log('🎓 Starting auto-enrollment process...');

    // Get all students and classes
    console.log('📊 Fetching students...');
    const { data: students, error: studentsError } = await supabase
      .from('users')
      .select('id, student_id, first_name, last_name, email')
      .eq('role', 'student')
      .eq('is_active', true);

    if (studentsError) {
      console.error('❌ Students fetch error:', studentsError);
      return res.status(500).json({
        error: 'Failed to fetch students',
        details: studentsError.message
      });
    }

    console.log('📊 Fetching classes...');
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name')
      .eq('is_active', true);

    if (classesError) {
      console.error('❌ Classes fetch error:', classesError);
      return res.status(500).json({
        error: 'Failed to fetch classes',
        details: classesError.message
      });
    }

    console.log(`📊 Found ${students?.length || 0} students and ${classes?.length || 0} classes`);

    // Create enrollment mappings based on student naming patterns
    const enrollments = [];
    const enrollmentLog = [];

    students?.forEach(student => {
      const studentId = student.student_id?.toLowerCase() || '';
      const firstName = student.first_name?.toLowerCase() || '';

      // Find matching class based on student ID patterns
      let matchedClass = null;

      // Pattern matching for different class formats
      if (studentId.includes('11nma') || firstName.includes('11nma')) {
        matchedClass = classes?.find(c => c.name === '11 NM A');
      } else if (studentId.includes('11nmb') || firstName.includes('11nmb')) {
        matchedClass = classes?.find(c => c.name === '11 NMB');
      } else if (studentId.includes('11nmc') || firstName.includes('11nmc')) {
        matchedClass = classes?.find(c => c.name === '11 NM C');
      } else if (studentId.includes('11coma') || firstName.includes('11coma')) {
        matchedClass = classes?.find(c => c.name === '11 COM A');
      } else if (studentId.includes('11comb') || firstName.includes('11comb')) {
        matchedClass = classes?.find(c => c.name === '11 COM B');
      } else if (studentId.includes('11comc') || firstName.includes('11comc')) {
        matchedClass = classes?.find(c => c.name === '11 COM C');
      } else if (studentId.includes('12coma') || firstName.includes('12coma')) {
        matchedClass = classes?.find(c => c.name === '12 COM A');
      } else if (studentId.includes('12comb') || firstName.includes('12comb')) {
        matchedClass = classes?.find(c => c.name === '12 COM B');
      } else if (studentId.includes('12comc') || firstName.includes('12comc')) {
        matchedClass = classes?.find(c => c.name === '12 COM C');
      } else if (studentId.includes('demo')) {
        // Enroll demo students in 11 NM A for testing
        matchedClass = classes?.find(c => c.name === '11 NM A');
      }

      if (matchedClass) {
        enrollments.push({
          student_id: student.id,
          class_id: matchedClass.id,
          enrollment_date: new Date().toISOString(),
          academic_year: '2024-25',
          is_active: true,
          enrolled_by: null, // Will be set to current user if available
          notes: 'Auto-enrolled based on student ID pattern'
        });

        enrollmentLog.push({
          studentName: `${student.first_name} ${student.last_name}`,
          studentId: student.student_id,
          className: matchedClass.name,
          classId: matchedClass.id
        });
      }
    });

    console.log(`📝 Prepared ${enrollments.length} enrollments`);

    if (enrollments.length === 0) {
      return res.json({
        message: 'No students matched class patterns',
        enrollmentsCreated: 0,
        availableClasses: classes?.map(c => c.name) || [],
        sampleStudentIds: students?.slice(0, 5).map(s => s.student_id) || []
      });
    }

    // Insert enrollments in batches
    const { data: insertedEnrollments, error: insertError } = await supabase
      .from('student_enrollments')
      .insert(enrollments)
      .select('*');

    if (insertError) {
      console.error('❌ Enrollment insertion error:', insertError);
      return res.status(500).json({
        error: 'Failed to create enrollments',
        details: insertError.message,
        preparedEnrollments: enrollments.length
      });
    }

    console.log(`✅ Successfully created ${insertedEnrollments?.length || 0} enrollments`);

    res.json({
      message: 'Auto-enrollment completed successfully',
      enrollmentsCreated: insertedEnrollments?.length || 0,
      enrollmentDetails: enrollmentLog,
      summary: {
        totalStudents: students?.length || 0,
        totalClasses: classes?.length || 0,
        enrollmentsCreated: insertedEnrollments?.length || 0,
        unmatchedStudents: (students?.length || 0) - enrollments.length
      }
    });

  } catch (error) {
    console.error('Auto-enrollment error:', error);
    res.status(500).json({ error: 'Auto-enrollment failed', details: error.message });
  }
});

// Simple test enrollment - enroll demo students in 11 NM A class
router.post('/test-enroll-demo', authenticateToken, async (req, res) => {
  try {
    console.log('🧪 Creating test enrollments for demo students...');

    // Get demo students and 11 NM A class
    const { data: demoStudents, error: studentsError } = await supabase
      .from('users')
      .select('id, student_id, first_name, last_name')
      .eq('role', 'student')
      .eq('is_active', true)
      .ilike('student_id', 'DEMO%');

    const { data: targetClass, error: classError } = await supabase
      .from('classes')
      .select('id, name')
      .eq('name', '11 NM A')
      .single();

    if (studentsError || classError || !targetClass) {
      return res.status(500).json({
        error: 'Failed to fetch demo data',
        studentsError: studentsError?.message,
        classError: classError?.message,
        targetClass: targetClass
      });
    }

    console.log(`Found ${demoStudents?.length || 0} demo students and class: ${targetClass.name}`);

    // Create enrollments for demo students
    const enrollments = demoStudents?.map(student => ({
      student_id: student.id,
      class_id: targetClass.id,
      enrollment_date: new Date().toISOString(),
      academic_year: '2024-25',
      is_active: true,
      notes: 'Test enrollment for demo students'
    })) || [];

    if (enrollments.length === 0) {
      return res.json({
        message: 'No demo students found',
        demoStudents: demoStudents
      });
    }

    // Insert enrollments
    const { data: insertedEnrollments, error: insertError } = await supabase
      .from('student_enrollments')
      .insert(enrollments)
      .select('*');

    if (insertError) {
      console.error('❌ Insert error:', insertError);
      return res.status(500).json({
        error: 'Failed to create test enrollments',
        details: insertError.message
      });
    }

    console.log(`✅ Created ${insertedEnrollments?.length || 0} test enrollments`);

    res.json({
      message: 'Test enrollments created successfully',
      enrollmentsCreated: insertedEnrollments?.length || 0,
      enrollments: insertedEnrollments,
      students: demoStudents,
      targetClass: targetClass
    });

  } catch (error) {
    console.error('Test enrollment error:', error);
    res.status(500).json({ error: 'Test enrollment failed', details: error.message });
  }
});

// Debug all enrollments in the system
router.get('/debug/all-enrollments', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 Debug: Fetching ALL enrollments in system...');

    // Get all enrollments with full details
    const { data: allEnrollments, error: enrollError } = await supabase
      .from('student_enrollments')
      .select(`
        *,
        users!student_enrollments_student_id_fkey(id, student_id, first_name, last_name, email, role),
        classes!inner(id, name)
      `)
      .eq('is_active', true);

    if (enrollError) {
      console.error('❌ Error fetching all enrollments:', enrollError);
      return res.status(500).json({
        error: 'Failed to fetch enrollments',
        details: enrollError.message
      });
    }

    console.log(`📊 Found ${allEnrollments?.length || 0} total active enrollments`);

    // Group by class
    const enrollmentsByClass = {};
    allEnrollments?.forEach(enrollment => {
      const className = enrollment.classes.name;
      const classId = enrollment.class_id;

      if (!enrollmentsByClass[className]) {
        enrollmentsByClass[className] = {
          classId: classId,
          className: className,
          students: []
        };
      }

      enrollmentsByClass[className].students.push({
        studentId: enrollment.users.student_id,
        firstName: enrollment.users.first_name,
        lastName: enrollment.users.last_name,
        email: enrollment.users.email,
        enrollmentId: enrollment.id,
        enrolledAt: enrollment.created_at
      });
    });

    res.json({
      message: 'All enrollments retrieved',
      totalEnrollments: allEnrollments?.length || 0,
      enrollmentsByClass: enrollmentsByClass,
      rawEnrollments: allEnrollments || []
    });

  } catch (error) {
    console.error('Debug all enrollments error:', error);
    res.status(500).json({ error: 'Debug failed', details: error.message });
  }
});

// Debug groups query
router.get('/debug/groups-query', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 Debug: Testing groups query...');

    // Test basic groups query
    const { data: basicGroups, error: basicError } = await supabase
      .from('groups')
      .select('*');

    if (basicError) {
      return res.json({
        step: 'basic_groups',
        error: basicError.message,
        groups: null
      });
    }

    console.log(`📊 Found ${basicGroups?.length || 0} groups in basic query`);

    // Test groups with classes
    const { data: groupsWithClasses, error: classError } = await supabase
      .from('groups')
      .select(`
        *,
        classes:class_id (id, name)
      `);

    if (classError) {
      return res.json({
        step: 'groups_with_classes',
        error: classError.message,
        basicGroups: basicGroups,
        groupsWithClasses: null
      });
    }

    console.log(`📊 Groups with classes query successful`);

    // Test groups with members
    const { data: groupsWithMembers, error: memberError } = await supabase
      .from('groups')
      .select(`
        *,
        group_members (
          id,
          user_id
        )
      `);

    if (memberError) {
      return res.json({
        step: 'groups_with_members',
        error: memberError.message,
        basicGroups: basicGroups,
        groupsWithClasses: groupsWithClasses,
        groupsWithMembers: null
      });
    }

    console.log(`📊 Groups with members query successful`);

    // Test full query
    const { data: fullGroups, error: fullError } = await supabase
      .from('groups')
      .select(`
        *,
        classes:class_id (id, name),
        group_members (
          id,
          user_id,
          users!group_members_user_id_fkey (id, first_name, last_name, student_id)
        )
      `);

    res.json({
      message: 'Groups query debug completed',
      basicGroups: basicGroups?.length || 0,
      groupsWithClasses: groupsWithClasses?.length || 0,
      groupsWithMembers: groupsWithMembers?.length || 0,
      fullQuery: {
        success: !fullError,
        error: fullError?.message || null,
        groups: fullGroups?.length || 0,
        sampleGroup: fullGroups?.[0] || null
      }
    });

  } catch (error) {
    console.error('Debug groups query error:', error);
    res.status(500).json({ error: 'Debug failed', details: error.message });
  }
});

// Debug group members table
router.get('/debug/group-members', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 Debug: Checking group_members table...');

    // Check if group_members table exists and has data
    const { data: allMembers, error: membersError } = await supabase
      .from('group_members')
      .select('*');

    if (membersError) {
      return res.json({
        tableExists: false,
        error: membersError.message,
        members: null
      });
    }

    console.log(`📊 Found ${allMembers?.length || 0} group members`);

    // Try to get members with user details
    const { data: membersWithUsers, error: usersError } = await supabase
      .from('group_members')
      .select(`
        *,
        users!group_members_user_id_fkey (id, first_name, last_name, student_id)
      `);

    res.json({
      message: 'Group members debug completed',
      tableExists: true,
      totalMembers: allMembers?.length || 0,
      members: allMembers || [],
      membersWithUsers: {
        success: !usersError,
        error: usersError?.message || null,
        data: membersWithUsers || null
      }
    });

  } catch (error) {
    console.error('Debug group members error:', error);
    res.status(500).json({ error: 'Debug failed', details: error.message });
  }
});

// Get dashboard stats for groups
router.get('/dashboard-stats', authenticateToken, async (req, res) => {
  try {
    // Try to get group stats from Supabase
    try {
      const { data: groups, error: groupsError } = await supabase
        .from('groups')
        .select('id, class_id, is_active');

      const { data: students, error: studentsError } = await supabase
        .from('users')
        .select('id, role')
        .eq('role', 'student')
        .eq('is_active', true);

      const { data: classes, error: classesError } = await supabase
        .from('classes')
        .select('id, name, is_active')
        .eq('is_active', true);

      if (groupsError || studentsError || classesError) {
        throw new Error('Database query failed');
      }

      const activeGroups = groups?.filter(g => g.is_active) || [];
      const totalStudents = students?.length || 0;
      const totalClasses = classes?.length || 0;
      const distinctClasses = [...new Set(activeGroups.map(g => g.class_id))].length;

      res.json({
        message: 'Dashboard stats retrieved successfully',
        stats: {
          totalGroups: activeGroups.length,
          totalStudents: totalStudents,
          totalClasses: totalClasses,
          distinctClasses: distinctClasses,
          averageGroupSize: activeGroups.length > 0 ? Math.round(totalStudents / activeGroups.length) : 0
        }
      });

    } catch (supabaseError) {
      console.log('Supabase dashboard stats fetch failed, using sample data:', supabaseError.message);
      
      // Provide sample dashboard stats
      const sampleStats = {
        totalGroups: 8,
        totalStudents: 45,
        totalClasses: 2,
        distinctClasses: 2,
        averageGroupSize: 6
      };

      res.json({
        message: 'Dashboard stats retrieved successfully (sample data)',
        stats: sampleStats
      });
    }

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
});

// Get all groups
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { class_id, page = 1, limit = 20 } = req.query;

    // Try to get groups from Supabase
    try {
      let query = supabase
        .from('groups')
        .select(`
          *,
          classes:class_id (id, name),
          group_members (
            id,
            user_id,
            users!group_members_user_id_fkey (id, first_name, last_name, student_id)
          )
        `);

      if (class_id) {
        query = query.eq('class_id', class_id);
      }

      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data: groups, error, count } = await query;

      if (error) {
        throw error;
      }

      // Transform groups data to match frontend expectations
      const transformedGroups = groups?.map(group => {
        console.log(`🔄 Transforming group: ${group.name}`);
        console.log('Raw group members:', group.group_members);

        const memberCount = group.group_members?.length || 0;
        const leaderMember = group.group_members?.find(member => member.user_id === group.leader_id);

        // Handle leader name with better fallback
        let leaderName = 'No Leader';
        if (leaderMember?.users) {
          const firstName = leaderMember.users.first_name || '';
          const lastName = leaderMember.users.last_name || '';
          leaderName = `${firstName} ${lastName}`.trim() || `Student ${leaderMember.users.student_id || 'Unknown'}`;
        }

        // Transform members with better name handling
        const transformedMembers = group.group_members?.map(member => {
          console.log('Processing member:', member);

          let memberName = 'Unknown Student';
          if (member.users) {
            const firstName = member.users.first_name || '';
            const lastName = member.users.last_name || '';
            memberName = `${firstName} ${lastName}`.trim() || `Student ${member.users.student_id || 'Unknown'}`;
          }

          return {
            id: member.user_id,
            name: memberName,
            firstName: member.users?.first_name || '',
            lastName: member.users?.last_name || '',
            studentId: member.users?.student_id || '',
            email: member.users?.email || '',
            isLeader: member.user_id === group.leader_id
          };
        }) || [];

        console.log('Transformed members:', transformedMembers);

        return {
          id: group.id,
          name: group.name,
          description: group.description,
          className: group.classes?.name || 'Unknown Class',
          classId: group.class_id,
          memberCount: memberCount,
          maxMembers: group.max_members || 50, // Default max members
          leaderId: group.leader_id,
          leaderName: leaderName,
          isActive: group.is_active,
          members: transformedMembers
        };
      }) || [];

      const totalPages = Math.ceil(count / limit);

      res.json({
        message: 'Groups retrieved successfully',
        groups: transformedGroups,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count || 0,
          pages: totalPages
        }
      });

    } catch (supabaseError) {
      console.log('Supabase groups fetch failed, using sample data:', supabaseError.message);
      
      // Provide sample groups data
      const sampleGroups = [
        {
          id: 'group-1',
          name: 'Team Alpha',
          description: 'Programming team for CS101',
          class_id: 'class-1-uuid',
          leader_id: 'student-1',
          is_active: true,
          created_at: new Date().toISOString(),
          classes: {
            id: 'class-1-uuid',
            name: 'Computer Science 101'
          },
          group_members: [
            {
              id: 'member-1',
              users: {
                id: 'student-1',
                first_name: 'John',
                last_name: 'Student',
                student_id: 'CS001'
              }
            },
            {
              id: 'member-2',
              users: {
                id: 'student-2',
                first_name: 'Jane',
                last_name: 'Student',
                student_id: 'CS002'
              }
            }
          ]
        },
        {
          id: 'group-2',
          name: 'Team Beta',
          description: 'Database team for CS101',
          class_id: 'class-1-uuid',
          leader_id: 'student-3',
          is_active: true,
          created_at: new Date().toISOString(),
          classes: {
            id: 'class-1-uuid',
            name: 'Computer Science 101'
          },
          group_members: [
            {
              id: 'member-3',
              users: {
                id: 'student-3',
                first_name: 'Bob',
                last_name: 'Student',
                student_id: 'CS003'
              }
            }
          ]
        }
      ];

      // Filter by class_id if provided
      let filteredGroups = sampleGroups;
      if (class_id) {
        filteredGroups = sampleGroups.filter(g => g.class_id === class_id);
      }

      res.json({
        message: 'Groups retrieved successfully (sample data)',
        groups: filteredGroups,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredGroups.length,
          pages: Math.ceil(filteredGroups.length / limit)
        }
      });
    }

  } catch (error) {
    console.error('Get groups error:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
});

// Get group by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Try to get group from Supabase
    try {
      const { data: groups, error } = await supabase
        .from('groups')
        .select(`
          *,
          classes:class_id (id, name, description),
          group_members (
            id,
            is_leader,
            users:user_id (id, first_name, last_name, student_id, email)
          )
        `)
        .eq('id', id)
        .limit(1);

      if (error) {
        throw error;
      }

      if (!groups || groups.length === 0) {
        return res.status(404).json({ error: 'Group not found' });
      }

      res.json({
        message: 'Group retrieved successfully',
        group: groups[0]
      });

    } catch (supabaseError) {
      console.log('Supabase group fetch failed, using fallback:', supabaseError.message);
      
      // Provide fallback group data
      const sampleGroup = {
        id: id,
        name: 'Demo Group',
        description: 'Demo group for testing',
        class_id: 'class-1-uuid',
        leader_id: 'student-1',
        is_active: true,
        created_at: new Date().toISOString(),
        classes: {
          id: 'class-1-uuid',
          name: 'Demo Class',
          description: 'Demo class for testing'
        },
        group_members: [
          {
            id: 'member-1',
            is_leader: true,
            users: {
              id: 'student-1',
              first_name: 'Demo',
              last_name: 'Student',
              student_id: 'DEMO001',
              email: '<EMAIL>'
            }
          }
        ]
      };

      res.json({
        message: 'Group retrieved successfully (fallback)',
        group: sampleGroup
      });
    }

  } catch (error) {
    console.error('Get group by ID error:', error);
    res.status(500).json({ error: 'Failed to fetch group' });
  }
});

// Create group
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { groupName, description, classId, leaderId, studentIds } = req.body;
    const user_id = req.user.id;

    console.log('🎯 Creating group:', { groupName, description, classId, leaderId, studentIds });

    // Create the group
    const { data: newGroup, error: groupError } = await supabase
      .from('groups')
      .insert({
        name: groupName,
        description: description || '',
        class_id: classId,
        leader_id: leaderId,
        is_active: true
      })
      .select('*')
      .single();

    if (groupError) {
      console.error('❌ Group creation error:', groupError);
      return res.status(500).json({
        error: 'Failed to create group',
        details: groupError.message
      });
    }

    console.log('✅ Group created:', newGroup);

    // Add group members
    if (studentIds && studentIds.length > 0) {
      console.log(`📝 Preparing to add ${studentIds.length} members to group ${newGroup.id}`);
      console.log('Student IDs:', studentIds);

      const memberInserts = studentIds.map(studentId => ({
        group_id: newGroup.id,
        user_id: studentId
      }));

      console.log('Member inserts:', memberInserts);

      const { data: members, error: membersError } = await supabase
        .from('group_members')
        .insert(memberInserts)
        .select('*');

      if (membersError) {
        console.error('❌ Group members creation error:', membersError);
        return res.status(500).json({
          error: 'Failed to add group members',
          details: membersError.message,
          groupCreated: true,
          groupId: newGroup.id
        });
      } else {
        console.log(`✅ Successfully added ${members?.length || 0} group members`);
        console.log('Created members:', members);
      }
    } else {
      console.log('⚠️ No student IDs provided for group members');
    }

    res.json({
      message: 'Group created successfully',
      group: newGroup
    });

  } catch (error) {
    console.error('Create group error:', error);
    res.status(500).json({ error: 'Failed to create group', details: error.message });
  }
});

// Update group
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { groupName, description, maxMembers } = req.body;

    console.log(`📝 Updating group ${id}:`, { groupName, description, maxMembers });

    // Update the group in the database (only update fields that exist)
    const updateData = {
      name: groupName,
      description: description || ''
    };

    // Only add max_members if it was provided (in case column doesn't exist)
    if (maxMembers !== undefined) {
      updateData.max_members = maxMembers;
    }

    console.log('Update data:', updateData);

    const { data: updatedGroup, error: updateError } = await supabase
      .from('groups')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (updateError) {
      console.error('❌ Error updating group:', updateError);
      return res.status(500).json({
        error: 'Failed to update group',
        details: updateError.message
      });
    }

    console.log('✅ Group updated successfully:', updatedGroup);

    res.json({
      message: 'Group updated successfully',
      group: updatedGroup
    });

  } catch (error) {
    console.error('Update group error:', error);
    res.status(500).json({ error: 'Failed to update group', details: error.message });
  }
});

// Delete group
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ Deleting group: ${id}`);

    // First, delete all group members
    const { error: membersDeleteError } = await supabase
      .from('group_members')
      .delete()
      .eq('group_id', id);

    if (membersDeleteError) {
      console.error('❌ Error deleting group members:', membersDeleteError);
      return res.status(500).json({
        error: 'Failed to delete group members',
        details: membersDeleteError.message
      });
    }

    console.log('✅ Group members deleted successfully');

    // Then, delete the group itself
    const { error: groupDeleteError } = await supabase
      .from('groups')
      .delete()
      .eq('id', id);

    if (groupDeleteError) {
      console.error('❌ Error deleting group:', groupDeleteError);
      return res.status(500).json({
        error: 'Failed to delete group',
        details: groupDeleteError.message
      });
    }

    console.log('✅ Group deleted successfully');

    res.json({
      message: 'Group deleted successfully'
    });

  } catch (error) {
    console.error('Delete group error:', error);
    res.status(500).json({ error: 'Failed to delete group', details: error.message });
  }
});

// Add member to group
router.post('/:groupId/members', authenticateToken, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { userId } = req.body;

    console.log(`👥 Adding member ${userId} to group ${groupId}`);

    // Check if member already exists
    const { data: existingMember, error: checkError } = await supabase
      .from('group_members')
      .select('*')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .single();

    if (existingMember) {
      return res.status(409).json({
        error: 'Student is already a member of this group'
      });
    }

    // Add the member
    const { data: newMember, error: insertError } = await supabase
      .from('group_members')
      .insert({
        group_id: groupId,
        user_id: userId
      })
      .select('*')
      .single();

    if (insertError) {
      console.error('❌ Error adding member:', insertError);
      return res.status(500).json({
        error: 'Failed to add member to group',
        details: insertError.message
      });
    }

    console.log('✅ Member added successfully:', newMember);

    res.json({
      message: 'Member added successfully',
      member: newMember
    });

  } catch (error) {
    console.error('Add member error:', error);
    res.status(500).json({ error: 'Failed to add member', details: error.message });
  }
});

// Remove member from group
router.delete('/:groupId/members/:userId', authenticateToken, async (req, res) => {
  try {
    const { groupId, userId } = req.params;

    console.log(`👥 Removing member ${userId} from group ${groupId}`);

    // Remove the member
    const { error: deleteError } = await supabase
      .from('group_members')
      .delete()
      .eq('group_id', groupId)
      .eq('user_id', userId);

    if (deleteError) {
      console.error('❌ Error removing member:', deleteError);
      return res.status(500).json({
        error: 'Failed to remove member from group',
        details: deleteError.message
      });
    }

    console.log('✅ Member removed successfully');

    res.json({
      message: 'Member removed successfully'
    });

  } catch (error) {
    console.error('Remove member error:', error);
    res.status(500).json({ error: 'Failed to remove member', details: error.message });
  }
});

// Update group leader
router.patch('/:groupId/leader', authenticateToken, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { leaderId } = req.body;

    console.log(`👑 Updating group ${groupId} leader to ${leaderId}`);

    // Update the group leader
    const { data: updatedGroup, error: updateError } = await supabase
      .from('groups')
      .update({ leader_id: leaderId })
      .eq('id', groupId)
      .select('*')
      .single();

    if (updateError) {
      console.error('❌ Error updating leader:', updateError);
      return res.status(500).json({
        error: 'Failed to update group leader',
        details: updateError.message
      });
    }

    console.log('✅ Group leader updated successfully:', updatedGroup);

    res.json({
      message: 'Group leader updated successfully',
      group: updatedGroup
    });

  } catch (error) {
    console.error('Update leader error:', error);
    res.status(500).json({ error: 'Failed to update leader', details: error.message });
  }
});

module.exports = router;
