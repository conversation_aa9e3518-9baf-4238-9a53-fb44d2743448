const express = require('express');
const router = express.Router();
const { supabase } = require('../config/supabase');
const { authenticateToken } = require('../middleware/auth');

// In-memory storage for mock distributions (until database table is created)
let mockDistributions = [
  {
    id: 'mock-1',
    assignmentId: 'assignment-1',
    assignmentName: 'Sample Programming Assignment',
    assignmentDescription: 'Create a simple calculator program',
    pdfFileName: 'calculator_assignment.pdf',
    classId: 'class-1',
    className: '11 NM A',
    assignmentType: 'group',
    groupId: 'group-1',
    groupName: 'Team Alpha',
    userId: null,
    studentName: null,
    scheduledDate: new Date().toISOString(),
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'scheduled',
    assignedAt: new Date().toISOString(),
    instructorName: 'Demo Instructor'
  },
  {
    id: 'mock-2',
    assignmentId: 'assignment-2',
    assignmentName: 'Database Design Project',
    assignmentDescription: 'Design a database schema for a library management system',
    pdfFileName: null,
    classId: 'class-1',
    className: '11 NM A',
    assignmentType: 'class',
    groupId: null,
    groupName: null,
    userId: null,
    studentName: null,
    scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    deadline: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'scheduled',
    assignedAt: new Date().toISOString(),
    instructorName: 'Demo Instructor'
  }
];

// Get all assignment distributions
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 Fetching assignment distributions...');

    // Try to get real data from Supabase first - using assignment_distributions table
    const { data: distributions, error } = await supabase
      .from('assignment_distributions')
      .select(`
        *,
        created_assignments!inner(id, name, description, pdf_filename, status),
        classes!inner(id, name),
        groups(id, name),
        users!assignment_distributions_user_id_fkey(id, first_name, last_name, student_id),
        distributor:users!assignment_distributions_distributed_by_fkey(id, first_name, last_name)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Supabase error fetching distributions:', error);
      console.log('📋 Falling back to mock data...');
    } else {
      console.log(`📊 Found ${distributions?.length || 0} distributions in database`);
    }

    if (!error && distributions && distributions.length > 0) {
      // Map the data to match frontend expectations
      const mappedDistributions = distributions.map(dist => ({
        id: dist.id,
        assignmentId: dist.assignment_id,
        assignmentName: dist.created_assignments?.name || 'Unknown Assignment',
        assignmentDescription: dist.created_assignments?.description || '',
        pdfFileName: dist.created_assignments?.pdf_filename,
        classId: dist.class_id,
        className: dist.classes?.name || 'Unknown Class',
        assignmentType: dist.assignment_type,
        groupId: dist.group_id,
        groupName: dist.groups?.name,
        userId: dist.user_id,
        studentName: dist.users ? `${dist.users.first_name} ${dist.users.last_name} (${dist.users.student_id})` : null,
        scheduledDate: dist.scheduled_date,
        deadline: dist.deadline,
        status: dist.status,
        assignedAt: dist.created_at,
        instructorName: dist.distributor ? `${dist.distributor.first_name} ${dist.distributor.last_name}` : 'Unknown Instructor'
      }));

      console.log(`✅ Returning ${mappedDistributions.length} real distributions`);
      return res.json({
        success: true,
        distributions: mappedDistributions,
        message: `Assignment distributions retrieved successfully (${mappedDistributions.length} distributions)`
      });
    }

    // Fallback to mock distributions if no real data
    console.log(`📋 Returning ${mockDistributions.length} mock distributions`);
    res.json({
      success: true,
      distributions: mockDistributions,
      message: `Assignment distributions retrieved successfully (${mockDistributions.length} mock distributions)`
    });

  } catch (error) {
    console.error('Error fetching assignment distributions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assignment distributions'
    });
  }
});

// Create new assignment distribution
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      assignmentId,
      classId,
      assignmentType,
      groupIds,
      userIds,
      scheduledDate,
      deadline
    } = req.body;

    if (!assignmentId || !classId || !assignmentType || !scheduledDate || !deadline) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Fetch assignment details to create a complete distribution record
    let assignmentName = 'Unknown Assignment';
    let assignmentDescription = '';
    let pdfFileName = '';

    try {
      const { data: assignment, error: assignmentError } = await supabase
        .from('created_assignments')
        .select('name, description, pdf_filename')
        .eq('id', assignmentId)
        .single();

      if (!assignmentError && assignment) {
        assignmentName = assignment.name;
        assignmentDescription = assignment.description;
        pdfFileName = assignment.pdf_filename;
      }
    } catch (err) {
      console.log('Could not fetch assignment details:', err.message);
    }

    // Fetch class details
    let className = 'Unknown Class';
    try {
      const { data: classData, error: classError } = await supabase
        .from('classes')
        .select('name')
        .eq('id', classId)
        .single();

      if (!classError && classData) {
        className = classData.name;
      }
    } catch (err) {
      console.log('Could not fetch class details:', err.message);
    }

    // Fetch group names if assignment type is group
    let groupNames = [];
    if (assignmentType === 'group' && groupIds && groupIds.length > 0) {
      try {
        const { data: groups, error: groupError } = await supabase
          .from('groups')
          .select('id, name')
          .in('id', groupIds);

        if (!groupError && groups) {
          groupNames = groups.map(group => group.name);
        }
      } catch (err) {
        console.log('Could not fetch group details:', err.message);
      }
    }

    // Fetch student names if assignment type is individual
    let studentNames = [];
    if (assignmentType === 'individual' && userIds && userIds.length > 0) {
      try {
        const { data: students, error: studentError } = await supabase
          .from('users')
          .select('id, first_name, last_name')
          .in('id', userIds);

        if (!studentError && students) {
          studentNames = students.map(student => `${student.first_name} ${student.last_name}`);
        }
      } catch (err) {
        console.log('Could not fetch student details:', err.message);
      }
    }

    // Try to save to real database first
    try {
      // For group assignments, create one record per group
      // For individual assignments, create one record per user
      // For class assignments, create one record for the entire class

      let insertData = [];

      if (assignmentType === 'class') {
        insertData.push({
          assignment_id: assignmentId,
          class_id: classId,
          assignment_type: assignmentType,
          group_id: null,
          user_id: null,
          scheduled_date: scheduledDate,
          deadline: deadline,
          status: 'scheduled',
          distributed_by: req.user.id
        });
      } else if (assignmentType === 'group' && groupIds && groupIds.length > 0) {
        insertData = groupIds.map(groupId => ({
          assignment_id: assignmentId,
          class_id: classId,
          assignment_type: assignmentType,
          group_id: groupId,
          user_id: null,
          scheduled_date: scheduledDate,
          deadline: deadline,
          status: 'scheduled',
          distributed_by: req.user.id
        }));
      } else if (assignmentType === 'individual' && userIds && userIds.length > 0) {
        insertData = userIds.map(userId => ({
          assignment_id: assignmentId,
          class_id: classId,
          assignment_type: assignmentType,
          group_id: null,
          user_id: userId,
          scheduled_date: scheduledDate,
          deadline: deadline,
          status: 'scheduled',
          distributed_by: req.user.id
        }));
      }

      const { data: newDistributions, error: insertError } = await supabase
        .from('assignment_distributions')
        .insert(insertData)
        .select();

      if (!insertError && newDistributions && newDistributions.length > 0) {
        // Successfully saved to database
        const distributionResponses = newDistributions.map(dist => ({
          id: dist.id,
          assignmentId: assignmentId,
          assignmentName: assignmentName,
          assignmentDescription: assignmentDescription,
          pdfFileName: pdfFileName,
          classId: classId,
          className: className,
          assignmentType: assignmentType,
          groupId: dist.group_id,
          groupName: assignmentType === 'group' ? (groupNames.find((_, index) => groupIds[index] === dist.group_id) || 'Unknown Group') : null,
          userId: dist.user_id,
          studentName: assignmentType === 'individual' ? (studentNames.find((_, index) => userIds[index] === dist.user_id) || 'Unknown Student') : null,
          scheduledDate: scheduledDate,
          deadline: deadline,
          status: 'scheduled',
          assignedAt: dist.created_at,
          instructorName: `${req.user.first_name} ${req.user.last_name}`
        }));

        return res.status(201).json({
          success: true,
          distributions: distributionResponses,
          count: distributionResponses.length,
          message: `Assignment distributed successfully to ${distributionResponses.length} ${assignmentType === 'class' ? 'class' : assignmentType === 'group' ? 'group(s)' : 'student(s)'}`
        });
      }
    } catch (dbError) {
      console.log('Database save failed, using mock storage:', dbError.message);
    }

    // Fallback to mock storage
    const mockDistribution = {
      id: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      assignmentId: assignmentId,
      assignmentName: assignmentName,
      assignmentDescription: assignmentDescription,
      pdfFileName: pdfFileName,
      classId: classId,
      className: className,
      assignmentType: assignmentType,
      groupId: groupIds && groupIds.length > 0 ? groupIds[0] : null,
      groupName: assignmentType === 'group' ? (groupNames.length > 0 ? groupNames.join(', ') : 'Unknown Group') : null,
      userId: userIds && userIds.length > 0 ? userIds[0] : null,
      studentName: assignmentType === 'individual' ? (studentNames.length > 0 ? studentNames.join(', ') : 'Unknown Student') : null,
      scheduledDate: scheduledDate,
      deadline: deadline,
      status: 'scheduled',
      assignedAt: new Date().toISOString(),
      instructorName: req.user.firstName + ' ' + req.user.lastName
    };

    // Store in mock distributions array
    mockDistributions.push(mockDistribution);

    res.status(201).json({
      success: true,
      distribution: mockDistribution,
      message: 'Assignment distribution created successfully (mock response - table not created yet)'
    });

  } catch (error) {
    console.error('Error creating assignment distributions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create assignment distributions'
    });
  }
});

// Update assignment distribution
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { scheduledDate, deadline, status } = req.body;

    // Find and update the distribution in mock storage
    const distributionIndex = mockDistributions.findIndex(d => d.id === id);

    if (distributionIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Assignment distribution not found'
      });
    }

    // Update the distribution
    mockDistributions[distributionIndex] = {
      ...mockDistributions[distributionIndex],
      scheduledDate: scheduledDate || mockDistributions[distributionIndex].scheduledDate,
      deadline: deadline || mockDistributions[distributionIndex].deadline,
      status: status || mockDistributions[distributionIndex].status,
      assignedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      distribution: mockDistributions[distributionIndex],
      message: 'Assignment distribution updated successfully (mock response - table not created yet)'
    });
  } catch (error) {
    console.error('Error updating assignment distribution:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update assignment distribution'
    });
  }
});

// Delete assignment distribution
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Find and remove the distribution from mock storage
    const distributionIndex = mockDistributions.findIndex(d => d.id === id);

    if (distributionIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Assignment distribution not found'
      });
    }

    // Remove the distribution
    mockDistributions.splice(distributionIndex, 1);

    res.json({
      success: true,
      message: 'Assignment distribution deleted successfully (mock response - table not created yet)'
    });
  } catch (error) {
    console.error('Error deleting assignment distribution:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete assignment distribution'
    });
  }
});

module.exports = router;
