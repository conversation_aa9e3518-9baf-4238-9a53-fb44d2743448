-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.class_lab_assignments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  class_id uuid NOT NULL,
  lab_id uuid NOT NULL,
  assigned_at timestamp with time zone DEFAULT now(),
  assigned_by character varying DEFAULT 'system'::character varying,
  is_active boolean DEFAULT true,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT class_lab_assignments_pkey PRIMARY KEY (id),
  CONSTRAINT class_lab_assignments_lab_id_fkey FOREIGN KEY (lab_id) REFERENCES public.labs(id),
  CONSTRAINT class_lab_assignments_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id)
);
CREATE TABLE public.classes (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name character varying NOT NULL,
  description text,
  grade_level integer,
  stream character varying,
  instructor_id uuid,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT classes_pkey PRIMARY KEY (id),
  CONSTRAINT classes_instructor_id_fkey FOREIGN KEY (instructor_id) REFERENCES public.users(id)
);
CREATE TABLE public.computers (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  lab_id uuid,
  computer_name character varying NOT NULL,
  seat_number character varying,
  specifications jsonb,
  status character varying DEFAULT 'available'::character varying CHECK (status::text = ANY (ARRAY['available'::character varying, 'occupied'::character varying, 'maintenance'::character varying, 'reserved'::character varying]::text[])),
  is_functional boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT computers_pkey PRIMARY KEY (id),
  CONSTRAINT computers_lab_id_fkey FOREIGN KEY (lab_id) REFERENCES public.labs(id)
);
CREATE TABLE public.created_assignments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name character varying NOT NULL,
  description text,
  pdf_filename character varying,
  pdf_path character varying,
  google_drive_file_id character varying,
  google_drive_view_link text,
  google_drive_download_link text,
  file_size bigint,
  mime_type character varying,
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'published'::character varying, 'archived'::character varying]::text[])),
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT created_assignments_pkey PRIMARY KEY (id),
  CONSTRAINT created_assignments_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.assignment_distributions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  assignment_id uuid NOT NULL,
  class_id uuid NOT NULL,
  assignment_type character varying NOT NULL CHECK (assignment_type::text = ANY (ARRAY['class'::character varying, 'group'::character varying, 'individual'::character varying]::text[])),
  group_id uuid,
  user_id uuid,
  scheduled_date timestamp with time zone NOT NULL,
  deadline timestamp with time zone NOT NULL,
  status character varying DEFAULT 'scheduled'::character varying CHECK (status::text = ANY (ARRAY['scheduled'::character varying, 'in_progress'::character varying, 'completed'::character varying, 'cancelled'::character varying]::text[])),
  distributed_by uuid,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT assignment_distributions_pkey PRIMARY KEY (id),
  CONSTRAINT assignment_distributions_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.created_assignments(id) ON DELETE CASCADE,
  CONSTRAINT assignment_distributions_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id) ON DELETE CASCADE,
  CONSTRAINT assignment_distributions_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.groups(id) ON DELETE SET NULL,
  CONSTRAINT assignment_distributions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL,
  CONSTRAINT assignment_distributions_distributed_by_fkey FOREIGN KEY (distributed_by) REFERENCES public.users(id) ON DELETE SET NULL,
  CONSTRAINT assignment_distributions_type_check CHECK (
    (assignment_type = 'class' AND group_id IS NULL AND user_id IS NULL) OR
    (assignment_type = 'group' AND group_id IS NOT NULL AND user_id IS NULL) OR
    (assignment_type = 'individual' AND group_id IS NULL AND user_id IS NOT NULL)
  )
);
CREATE TABLE public.grades (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  submission_id uuid,
  graded_by uuid,
  score numeric,
  max_score numeric DEFAULT 100.00,
  feedback text,
  grade_scale character varying,
  graded_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT grades_pkey PRIMARY KEY (id),
  CONSTRAINT grades_graded_by_fkey FOREIGN KEY (graded_by) REFERENCES public.users(id),
  CONSTRAINT grades_submission_id_fkey FOREIGN KEY (submission_id) REFERENCES public.submissions(id)
);
CREATE TABLE public.group_members (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  group_id uuid,
  user_id uuid,
  joined_at timestamp with time zone DEFAULT now(),
  CONSTRAINT group_members_pkey PRIMARY KEY (id),
  CONSTRAINT group_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT group_members_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.groups(id)
);
CREATE TABLE public.groups (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name character varying NOT NULL,
  description text,
  class_id uuid,
  leader_id uuid,
  max_members integer DEFAULT 4,
  is_default boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT groups_pkey PRIMARY KEY (id),
  CONSTRAINT groups_leader_id_fkey FOREIGN KEY (leader_id) REFERENCES public.users(id),
  CONSTRAINT groups_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id)
);
CREATE TABLE public.labs (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name character varying NOT NULL,
  description text,
  capacity integer NOT NULL DEFAULT 50,
  location character varying,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT labs_pkey PRIMARY KEY (id)
);
CREATE TABLE public.schedules (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  title character varying NOT NULL,
  description text,
  assignment_id uuid,
  class_id uuid,
  group_id uuid,
  lab_id uuid,
  instructor_id uuid,
  scheduled_date timestamp with time zone NOT NULL,
  deadline timestamp with time zone,
  status character varying DEFAULT 'scheduled'::character varying CHECK (status::text = ANY (ARRAY['scheduled'::character varying, 'in_progress'::character varying, 'completed'::character varying, 'cancelled'::character varying]::text[])),
  assignee_type character varying NOT NULL CHECK (assignee_type::text = ANY (ARRAY['class'::character varying, 'group'::character varying, 'individual'::character varying]::text[])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  start_time time without time zone,
  end_time time without time zone,
  CONSTRAINT schedules_pkey PRIMARY KEY (id),
  CONSTRAINT schedules_instructor_id_fkey FOREIGN KEY (instructor_id) REFERENCES public.users(id),
  CONSTRAINT schedules_lab_id_fkey FOREIGN KEY (lab_id) REFERENCES public.labs(id),
  CONSTRAINT schedules_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.groups(id),
  CONSTRAINT schedules_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id),
  CONSTRAINT schedules_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.created_assignments(id),
  CONSTRAINT schedules_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.seat_assignments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  student_id uuid NOT NULL,
  seat_id character varying NOT NULL,
  lab_id uuid NOT NULL,
  class_id uuid,
  schedule_id uuid,
  assigned_at timestamp with time zone DEFAULT now(),
  unassigned_at timestamp with time zone,
  is_active boolean DEFAULT true,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT seat_assignments_pkey PRIMARY KEY (id),
  CONSTRAINT seat_assignments_lab_id_fkey FOREIGN KEY (lab_id) REFERENCES public.labs(id),
  CONSTRAINT seat_assignments_schedule_id_fkey FOREIGN KEY (schedule_id) REFERENCES public.schedules(id),
  CONSTRAINT seat_assignments_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id),
  CONSTRAINT seat_assignments_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.users(id)
);
CREATE TABLE public.student_enrollments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  student_id uuid NOT NULL,
  class_id uuid NOT NULL,
  enrollment_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
  academic_year character varying NOT NULL DEFAULT '2024-25'::character varying,
  is_active boolean DEFAULT true,
  enrolled_by uuid,
  notes text,
  CONSTRAINT student_enrollments_pkey PRIMARY KEY (id),
  CONSTRAINT student_enrollments_enrolled_by_fkey FOREIGN KEY (enrolled_by) REFERENCES public.users(id),
  CONSTRAINT student_enrollments_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id),
  CONSTRAINT student_enrollments_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.users(id)
);
CREATE TABLE public.submissions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  schedule_id uuid,
  student_id uuid,
  group_id uuid,
  submission_type character varying NOT NULL CHECK (submission_type::text = ANY (ARRAY['file'::character varying, 'text'::character varying, 'both'::character varying]::text[])),
  file_path character varying,
  text_content text,
  google_drive_file_id character varying,
  google_drive_view_link text,
  google_drive_download_link text,
  file_size bigint,
  mime_type character varying,
  original_filename character varying,
  status character varying DEFAULT 'draft'::character varying CHECK (status::text = ANY (ARRAY['draft'::character varying, 'submitted'::character varying, 'late'::character varying, 'graded'::character varying]::text[])),
  submitted_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT submissions_pkey PRIMARY KEY (id),
  CONSTRAINT submissions_schedule_id_fkey FOREIGN KEY (schedule_id) REFERENCES public.schedules(id),
  CONSTRAINT submissions_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.groups(id),
  CONSTRAINT submissions_student_id_fkey FOREIGN KEY (student_id) REFERENCES public.users(id)
);
CREATE TABLE public.timetable_config (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  start_time time without time zone NOT NULL DEFAULT '08:00:00'::time without time zone,
  end_time time without time zone NOT NULL DEFAULT '17:00:00'::time without time zone,
  lecture_duration_minutes integer NOT NULL DEFAULT 50,
  break_duration_minutes integer NOT NULL DEFAULT 10,
  number_of_lectures integer NOT NULL DEFAULT 8,
  number_of_breaks integer NOT NULL DEFAULT 2,
  break_after_periods ARRAY DEFAULT ARRAY[0, 4],
  academic_year_start date DEFAULT '2024-04-01'::date,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT timetable_config_pkey PRIMARY KEY (id)
);
CREATE TABLE public.timetable_versions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  version_number character varying NOT NULL,
  version_name character varying NOT NULL,
  description text,
  effective_from date NOT NULL,
  effective_until date,
  is_active boolean DEFAULT false,
  created_by uuid,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT timetable_versions_pkey PRIMARY KEY (id),
  CONSTRAINT timetable_versions_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  email character varying NOT NULL UNIQUE,
  password_hash character varying NOT NULL,
  first_name character varying NOT NULL,
  last_name character varying NOT NULL,
  role character varying NOT NULL CHECK (role::text = ANY (ARRAY['admin'::character varying, 'instructor'::character varying, 'student'::character varying]::text[])),
  student_id character varying UNIQUE,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (id)
);