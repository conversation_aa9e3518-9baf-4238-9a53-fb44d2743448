-- Migration: Add assignment_distributions table for assignment management
-- This table handles the distribution of assignments to classes, groups, or individual students

-- Create assignment_distributions table
CREATE TABLE IF NOT EXISTS public.assignment_distributions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  assignment_id uuid NOT NULL,
  class_id uuid NOT NULL,
  assignment_type character varying NOT NULL CHECK (assignment_type::text = ANY (ARRAY['class'::character varying, 'group'::character varying, 'individual'::character varying]::text[])),
  group_id uuid,
  user_id uuid,
  scheduled_date timestamp with time zone NOT NULL,
  deadline timestamp with time zone NOT NULL,
  status character varying DEFAULT 'scheduled'::character varying CHECK (status::text = ANY (ARRAY['scheduled'::character varying, 'in_progress'::character varying, 'completed'::character varying, 'cancelled'::character varying]::text[])),
  distributed_by uuid,
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Primary key
  CONSTRAINT assignment_distributions_pkey PRIMARY KEY (id),
  
  -- Foreign key constraints
  CONSTRAINT assignment_distributions_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.created_assignments(id) ON DELETE CASCADE,
  CONSTRAINT assignment_distributions_class_id_fkey FOREIGN KEY (class_id) REFERENCES public.classes(id) ON DELETE CASCADE,
  CONSTRAINT assignment_distributions_group_id_fkey FOREIGN KEY (group_id) REFERENCES public.groups(id) ON DELETE SET NULL,
  CONSTRAINT assignment_distributions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL,
  CONSTRAINT assignment_distributions_distributed_by_fkey FOREIGN KEY (distributed_by) REFERENCES public.users(id) ON DELETE SET NULL,
  
  -- Business logic constraints
  CONSTRAINT assignment_distributions_type_check CHECK (
    (assignment_type = 'class' AND group_id IS NULL AND user_id IS NULL) OR
    (assignment_type = 'group' AND group_id IS NOT NULL AND user_id IS NULL) OR
    (assignment_type = 'individual' AND group_id IS NULL AND user_id IS NOT NULL)
  )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_assignment_id ON public.assignment_distributions(assignment_id);
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_class_id ON public.assignment_distributions(class_id);
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_group_id ON public.assignment_distributions(group_id);
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_user_id ON public.assignment_distributions(user_id);
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_status ON public.assignment_distributions(status);
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_scheduled_date ON public.assignment_distributions(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_assignment_distributions_deadline ON public.assignment_distributions(deadline);

-- Create trigger for updated_at timestamp
CREATE OR REPLACE FUNCTION update_assignment_distributions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_assignment_distributions_updated_at
    BEFORE UPDATE ON public.assignment_distributions
    FOR EACH ROW
    EXECUTE FUNCTION update_assignment_distributions_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.assignment_distributions IS 'Stores assignment distributions to classes, groups, or individual students';
COMMENT ON COLUMN public.assignment_distributions.assignment_type IS 'Type of assignment distribution: class, group, or individual';
COMMENT ON COLUMN public.assignment_distributions.group_id IS 'Group ID for group assignments (null for class/individual)';
COMMENT ON COLUMN public.assignment_distributions.user_id IS 'User ID for individual assignments (null for class/group)';
COMMENT ON COLUMN public.assignment_distributions.scheduled_date IS 'When the assignment becomes available to students';
COMMENT ON COLUMN public.assignment_distributions.deadline IS 'Assignment submission deadline';
COMMENT ON COLUMN public.assignment_distributions.status IS 'Current status of the assignment distribution';

-- Insert some sample data for testing (optional)
-- INSERT INTO public.assignment_distributions (
--   assignment_id, 
--   class_id, 
--   assignment_type, 
--   scheduled_date, 
--   deadline, 
--   distributed_by
-- ) VALUES (
--   (SELECT id FROM public.created_assignments LIMIT 1),
--   (SELECT id FROM public.classes LIMIT 1),
--   'class',
--   now(),
--   now() + interval '7 days',
--   (SELECT id FROM public.users WHERE role = 'instructor' LIMIT 1)
-- );
